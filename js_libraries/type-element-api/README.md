# Introduction

@lynx-js/type-element-api is a type package of all Element API officially provided by the Lynx team. Using this package can help you better use Element API to develop your framework.

# Usage

## For Framework Developers

```json
"peerDependencies": {
  "@lynx-js/type-element-api": "latest"
}
```

## For Product Developers

```json
"devDependencies": {
  "@lynx-js/type-element-api": "latest"
}
```

After installing the dependencies, you can use them directly, for example:

```typescript
let pid = 10;
let viewRef = __CreateView(10);
```
