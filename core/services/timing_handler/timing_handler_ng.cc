// Copyright 2024 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

#include "core/services/timing_handler/timing_handler_ng.h"

#include <unordered_set>
#include <utility>

#include "base/include/log/logging.h"
#include "core/services/timing_handler/timing_constants.h"
#include "core/services/timing_handler/timing_constants_deprecated.h"

namespace lynx {
namespace tasm {
namespace timing {

TimingHandlerNg::TimingHandlerNg(TimingHandlerDelegate* delegate)
    : delegate_(delegate) {
  if (delegate_) {
    timing_info_.SetValueFactory(delegate_->GetValueFactory());
  }
}

void TimingHandlerNg::OnPipelineStart(
    const PipelineID& pipeline_id, const PipelineOrigin& pipeline_origin,
    const TimestampUs pipeline_start_timestamp) {
  pipeline_id_to_origin_map_.emplace(pipeline_id, pipeline_origin);
  if (pipeline_origin == kReloadBundleFromNative ||
      pipeline_origin == kLoadBundle ||
      pipeline_origin == kReloadBundleFromBts) {
    timing_info_.SetLoadBundlePipelineId(pipeline_id);
  }
}

void TimingHandlerNg::BindPipelineIDWithTimingFlag(
    const PipelineID& pipeline_id, const TimingFlag& timing_flag) {
  if (timing_flag.empty() || pipeline_id.empty()) {
    return;
  }
  pipeline_id_to_timing_flags_map_[pipeline_id].emplace_back(timing_flag);
}

// Methods for setting timing information.
void TimingHandlerNg::SetFrameworkTiming(const TimestampKey& timing_key,
                                         const TimestampUs us_timestamp,
                                         const PipelineID& pipeline_id) {
  timing_info_.SetFrameworkTiming(timing_key, us_timestamp, pipeline_id);
}

void TimingHandlerNg::SetFrameworkExtraTimingInfo(const PipelineID& pipeline_id,
                                                  const std::string& key,
                                                  const std::string& value) {
  timing_info_.SetFrameworkExtraTimingInfo(pipeline_id, key, value);
}

void TimingHandlerNg::SetTiming(const TimestampKey& timing_key,
                                const TimestampUs us_timestamp,
                                const PipelineID& pipeline_id) {
  if (timing_key.empty() || us_timestamp == 0) {
    LOGE("Invalid timing key or timestamp in TimingHandlerNg::SetTiming");
    return;
  }
  if (pipeline_id.empty()) {
    ProcessInitTiming(timing_key, us_timestamp);
  } else {
    ProcessPipelineTiming(timing_key, us_timestamp, pipeline_id);
  }
}

void TimingHandlerNg::SetTimingWithTimingFlag(
    const tasm::timing::TimingFlag& timing_flag,
    const std::string& timestamp_key,
    const tasm::timing::TimestampUs timestamp) {
  timing_info_.SetTimingWithTimingFlag(timing_flag, timestamp_key, timestamp);
}

void TimingHandlerNg::ProcessInitTiming(
    const lynx::tasm::timing::TimestampKey& timing_key,
    const lynx::tasm::timing::TimestampUs us_timestamp) {
  if (timing_info_.SetInitTiming(timing_key, us_timestamp)) {
    DispatchPerformanceEventIfNeeded(timing_key);
  }
}

void TimingHandlerNg::ProcessPipelineTiming(
    const lynx::tasm::timing::TimestampKey& timing_key,
    const lynx::tasm::timing::TimestampUs us_timestamp,
    const lynx::tasm::PipelineID& pipeline_id) {
  if (timing_info_.SetPipelineTiming(timing_key, us_timestamp, pipeline_id)) {
    if (timing_key == kLoadBackgroundEnd) {
      is_background_runtime_ready_ = true;
      FlushPendingPerformanceEntries();
    }
    DispatchPerformanceEventIfNeeded(timing_key, pipeline_id);
  }
  // TODO(zhangkaijie.9): temporarily regard PaintEnd as PipelineEnd.
  if (timing_key == kPaintEnd) {
    ProcessPipelineTiming(kPipelineEnd, us_timestamp, pipeline_id);
  }
}

bool TimingHandlerNg::IsLoadBundlePipeline(
    const PipelineID& pipeline_id) const {
  return pipeline_id == timing_info_.GetLoadBundlePipelineId();
}

/*
 * Reset all timing information.
 */
void TimingHandlerNg::ClearAllTimingInfo() {
  timing_info_.ClearAllTimingInfo();
  has_dispatched_timing_flags_.clear();
  pending_dispatched_performance_entries_.clear();
  is_background_runtime_ready_ = false;
}

/*
 * Dispatch PerformanceEntry
 */
void TimingHandlerNg::DispatchPerformanceEventIfNeeded(
    const TimestampKey& timing_key, const lynx::tasm::PipelineID& pipeline_id) {
  if (!pipeline_id.empty()) {
    DispatchPipelineEntryIfNeeded(timing_key, pipeline_id);
  } else {
    DispatchInitContainerEntryIfNeeded(timing_key);
    DispatchInitLynxViewEntryIfNeeded(timing_key);
    DispatchInitBackgroundRuntimeEntryIfNeeded(timing_key);
  }
  DispatchMetricFcpEntryIfNeeded(timing_key, pipeline_id);
  DispatchMetricFmpEntryIfNeeded(timing_key, pipeline_id);
}

void TimingHandlerNg::DispatchInitContainerEntryIfNeeded(
    const TimestampKey& current_key) {
  auto init_container_entry = timing_info_.GetInitContainerEntry(current_key);
  if (init_container_entry != nullptr) {
    init_container_entry->PushStringToMap(kEntryType, kEntryTypeInit);
    init_container_entry->PushStringToMap(kEntryName, kEntryNameContainer);
    SendPerformanceEntry(std::move(init_container_entry));
  }
}

void TimingHandlerNg::DispatchInitLynxViewEntryIfNeeded(
    const TimestampKey& current_key) {
  auto init_lynxview_entry = timing_info_.GetInitLynxViewEntry(current_key);
  if (init_lynxview_entry != nullptr) {
    init_lynxview_entry->PushStringToMap(kEntryType, kEntryTypeInit);
    init_lynxview_entry->PushStringToMap(kEntryName, kEntryNameLynxView);
    SendPerformanceEntry(std::move(init_lynxview_entry));
  }
}

void TimingHandlerNg::DispatchInitBackgroundRuntimeEntryIfNeeded(
    const TimestampKey& current_key) {
  auto init_background_runtime_entry =
      timing_info_.GetInitBackgroundRuntimeEntry(current_key);
  if (init_background_runtime_entry != nullptr) {
    init_background_runtime_entry->PushStringToMap(kEntryType, kEntryTypeInit);
    init_background_runtime_entry->PushStringToMap(kEntryName,
                                                   kEntryNameBackgroundRuntime);
    SendPerformanceEntry(std::move(init_background_runtime_entry));
  }
}

void TimingHandlerNg::DispatchMetricFcpEntryIfNeeded(
    const TimestampKey& current_key, const PipelineID& pipeline_id) {
  if (!pipeline_id.empty() && !IsLoadBundlePipeline(pipeline_id)) {
    return;
  }
  auto entry = timing_info_.GetMetricFcpEntry(current_key, pipeline_id);
  if (entry == nullptr) {
    return;
  }
  entry->PushStringToMap(kEntryType, kEntryTypeMetric);
  entry->PushStringToMap(kEntryName, kEntryNameFCP);
  SendPerformanceEntry(std::move(entry));
}

void TimingHandlerNg::DispatchMetricFmpEntryIfNeeded(
    const TimestampKey& current_key,
    const lynx::tasm::PipelineID& pipeline_id) {
  if (!pipeline_id.empty()) {
    auto it = pipeline_id_to_timing_flags_map_.find(pipeline_id);
    if (it == pipeline_id_to_timing_flags_map_.end()) {
      return;
    }
    bool is_fmp_pipeline =
        false;  // Iterate over the vector of TimingFlags for the specific ID
    for (const TimingFlag& flag : it->second) {
      if (flag == kLynxTimingActualFMPFlag) {
        is_fmp_pipeline = true;
        break;
      }
    }
    if (!is_fmp_pipeline) {
      return;
    }
  }

  auto entry = timing_info_.GetMetricFmpEntry(current_key, pipeline_id);
  if (entry == nullptr) {
    return;
  }
  entry->PushStringToMap(kEntryType, kEntryTypeMetric);
  entry->PushStringToMap(kEntryName, kEntryNameActualFMP);
  SendPerformanceEntry(std::move(entry));
}

void TimingHandlerNg::DispatchLoadBundleEntryIfNeeded(
    const TimestampKey& current_key,
    const lynx::tasm::PipelineID& pipeline_id) {
  const auto& name = pipeline_id_to_origin_map_.find(pipeline_id);
  auto timing_flag_iter = pipeline_id_to_timing_flags_map_.find(pipeline_id);
  bool is_timing_flags_empty =
      (timing_flag_iter == pipeline_id_to_timing_flags_map_.end());

  auto process_load_bundle_entry = [&](auto load_bundle_entry,
                                       const std::string& flag = {}) {
    load_bundle_entry->PushStringToMap(kEntryType, kEntryTypePipeline);
    if (name != pipeline_id_to_origin_map_.end()) {
      load_bundle_entry->PushStringToMap(kEntryName, name->second);
    } else {
      load_bundle_entry->PushStringToMap(kEntryName, kEntryNameLoadBundle);
    }
    if (!flag.empty()) {
      load_bundle_entry->PushStringToMap(kIdentifier, flag);
    }
    SendPerformanceEntry(std::move(load_bundle_entry));
  };

  if (is_timing_flags_empty) {
    auto load_bundle_entry =
        timing_info_.GetLoadBundleEntry(current_key, pipeline_id);
    if (load_bundle_entry != nullptr) {
      process_load_bundle_entry(std::move(load_bundle_entry));
    }
    return;
  } else {
    // Iterate over the vector of TimingFlags for the specific ID
    for (const TimingFlag& flag : timing_flag_iter->second) {
      if (has_dispatched_timing_flags_.count(flag)) {
        continue;
      }
      auto load_bundle_entry =
          timing_info_.GetLoadBundleEntry(current_key, pipeline_id);
      if (load_bundle_entry == nullptr) {
        continue;
      }
      process_load_bundle_entry(std::move(load_bundle_entry), flag);
      has_dispatched_timing_flags_.emplace(flag);
    }
  }
}

void TimingHandlerNg::DispatchPipelineEntryIfNeeded(
    const TimestampKey& current_key,
    const lynx::tasm::PipelineID& pipeline_id) {
  if (IsLoadBundlePipeline(pipeline_id)) {
    DispatchLoadBundleEntryIfNeeded(current_key, pipeline_id);
    return;
  }

  auto timing_flag_iter = pipeline_id_to_timing_flags_map_.find(pipeline_id);
  if (timing_flag_iter == pipeline_id_to_timing_flags_map_.end()) {
    return;
  }
  const auto& name = pipeline_id_to_origin_map_.find(pipeline_id);
  /* A PipelineID may correspond to multiple flags. The rule for releasing a
   * PipelineID is that all flags corresponding to this PipelineID have been
   * dispatched.
   *
   * Based on the principle that each TimingFlag can only send performance
   * events once, the following two release methods are defined:
   *
   * anyTimingFlagHasDispatched: If a TimingFlag corresponding to this
   * pipelineId has sent performance events in this dispatch, then the
   * corresponding timing data for this pipelineId no longer holds value and can
   * be released.
   *
   * allTimingFlagHasDispatched: If all TimingFlags corresponding to this
   * pipelineId have been dispatched, then all timing data corresponding to this
   * pipelineId is useless and can be released.
   */
  bool anyTimingFlagHasDispatched = false;
  bool allTimingFlagHasDispatched = true;
  for (const TimingFlag& flag : timing_flag_iter->second) {
    if (has_dispatched_timing_flags_.count(flag)) {
      continue;
    } else {
      allTimingFlagHasDispatched = false;
    }
    auto pipeline_entry =
        timing_info_.GetPipelineEntry(current_key, pipeline_id, flag);
    if (pipeline_entry == nullptr) {
      continue;
    }

    pipeline_entry->PushStringToMap(kEntryType, kEntryTypePipeline);
    if (name != pipeline_id_to_origin_map_.end()) {
      pipeline_entry->PushStringToMap(kEntryName, name->second);
    } else {
      pipeline_entry->PushStringToMap(kEntryName, kEntryTypePipeline);
    }
    pipeline_entry->PushStringToMap(kIdentifier, flag);
    SendPerformanceEntry(std::move(pipeline_entry));
    has_dispatched_timing_flags_.emplace(flag);
    anyTimingFlagHasDispatched = true;
  }
  if (anyTimingFlagHasDispatched || allTimingFlagHasDispatched) {
    ReleaseTiming(pipeline_id);
  }
}

void TimingHandlerNg::FlushPendingPerformanceEntries() {
  if (!delegate_) {
    pending_dispatched_performance_entries_.clear();
    return;
  }

  auto temp_pending_entries =
      std::move(pending_dispatched_performance_entries_);
  for (auto& entry : temp_pending_entries) {
    if (entry) {
      delegate_->OnPerformanceEvent(std::move(entry),
                                    timing_info_.GetEnableEngineCallback());
    }
  }
}

void TimingHandlerNg::SendPerformanceEntry(
    std::unique_ptr<lynx::pub::Value> entry) {
  if (!delegate_) {
    return;
  }

  if (ReadyToDispatch()) {
    delegate_->OnPerformanceEvent(std::move(entry),
                                  timing_info_.GetEnableEngineCallback());
  } else {
    pending_dispatched_performance_entries_.emplace_back(std::move(entry));
  }
}

bool TimingHandlerNg::ReadyToDispatch() const {
  return is_background_runtime_ready_ ||
         !timing_info_.GetEnableBackgroundRuntime();
}

void TimingHandlerNg::ReleaseTiming(const PipelineID& pipeline_id) {
  pipeline_id_to_timing_flags_map_.erase(pipeline_id);
  pipeline_id_to_origin_map_.erase(pipeline_id);
  timing_info_.ReleaseTiming(pipeline_id);
}

}  // namespace timing
}  // namespace tasm
}  // namespace lynx
